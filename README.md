# 超星ChaoXing回调监听器

## 项目简介

这是一个用于接收超星学习通ChaoXing回调请求的Windows服务程序，支持自动启动VIC Unity应用程序，并提供多种IPC通信方式。

## 主要功能

- **HTTP回调监听**: 监听超星学习通的OAuth回调请求
- **自动应用启动**: 通过自定义协议启动VIC Unity应用
- **多种IPC通信**: 支持命名管道、TCP Socket、文件共享三种通信方式
- **安全验证**: 支持handshake参数验证，确保请求安全性
- **用户友好界面**: 提供美观的HTML响应界面
- **Windows服务**: 可作为Windows服务运行

## 技术特性

### 监听地址
- 默认监听地址: `http://127.0.0.1:56123/`
- ChaoXing2登录地址: `http://127.0.0.1:56123/login/ChaoXing2`
- OAuth2登录地址: `http://127.0.0.1:56123/login/oauth2`

### IPC通信方式
1. **命名管道**: `VICChaoXingPipe`
2. **TCP Socket**: 端口 `56124`
3. **文件共享**: 使用 `ChaoXing_callback.json` 文件

### 安全验证
- 支持MD5 handshake验证
- 验证公式: `md5(secret_key+fid+resourceId+userId+confSubjectId+date('yyyyMMdd')+classId)`
- 支持跨日期验证（当前日期和前一天）

## 最近修复

### 状态显示问题修复

**问题描述**: 在HTML响应页面中，处理状态、处理时间和协议URL等信息没有正确显示真实数据。

**修复内容**:
1. **GetSuccessHtml方法**: 
   - 添加了 `currentTime` 变量来替代直接使用 `DateTime.Now`
   - 确保 `statusIcon`、`appStatus` 和 `customProtocolUrl` 变量正确显示
   - 修复了处理时间显示问题

2. **GetErrorHtml方法**:
   - 添加了 `currentTime` 变量来替代直接使用 `DateTime.Now`
   - 确保错误时间正确显示

3. **JSON响应方法**:
   - 修复了 `GetSuccessJson` 和 `GetErrorJson` 方法中的时间戳显示
   - 使用变量替代直接的 `DateTime.Now` 调用

**修复效果**:
- ✅ 处理状态现在正确显示图标和状态文本
- ✅ 处理时间现在显示实际的当前时间
- ✅ 协议URL现在正确显示完整的自定义协议链接
- ✅ 所有时间戳在HTML和JSON响应中都正确显示

## 使用方法

### 作为控制台应用运行
```bash
dotnet run
```

### 作为Windows服务安装
```bash
sc create ChaoXingCallbackListener binPath="路径\到\ChaoXingCallbackListener.exe"
sc start ChaoXingCallbackListener
```

### 配置URL保留（管理员权限）
```bash
netsh http add urlacl url=http://127.0.0.1:56123/ user=Everyone
```

## 配置参数

### 可配置项
- `_url`: 监听地址（默认: `http://127.0.0.1:56123/`）
- `_ipcPort`: IPC通信端口（默认: `56124`）
- `_pipeName`: 命名管道名称（默认: `VICChaoXingPipe`）
- `_secretKey`: 验证密钥（需要替换为实际密钥）

### 数据目录
- 主目录: `%ProgramData%\VICUnity`
- 备用目录: `%TEMP%\VICUnity`

## 支持的参数

### ChaoXing回调参数
- `fid`: 管理平台单位ID
- `confSubjectId`: 管理平台课程ID
- `classId`: 管理平台班级ID
- `userId`: 管理平台用户ID
- `usertype`: 用户类型
- `resourceId`: 管理平台资源ID
- `handshake`: 验证参数
- `mode`: 模式参数

## 系统要求

- .NET 6.0 或更高版本
- Windows 操作系统
- 管理员权限（用于URL保留和服务安装）

## 日志记录

程序使用Microsoft.Extensions.Logging进行日志记录，支持：
- 请求处理日志
- 错误异常日志
- IPC通信日志
- 应用启动日志

## 版本信息

- **版本**: v1.0
- **最后更新**: 2024年
- **开发团队**: VIC Unity Project

## 许可证

© 2024 VIC Unity Project

