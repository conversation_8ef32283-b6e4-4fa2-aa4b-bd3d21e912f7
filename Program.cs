using System;
using System.IO;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Web;
using System.Collections.Specialized;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;
using System.Collections.Generic;
using System.IO.Pipes;
using System.Net.Sockets;
using System.Security.Cryptography;
using System.Diagnostics;
using Microsoft.Win32;

namespace ChaoXingCallbackListener
{
    public class Program
    {
        public static void Main(string[] args)
        {
            CreateHostBuilder(args).Build().Run();
        }

        public static IHostBuilder CreateHostBuilder(string[] args) =>
            Host.CreateDefaultBuilder(args)
                .UseWindowsService(options =>
                {
                    options.ServiceName = "ChaoXingCallbackListener";
                })
                .ConfigureServices((hostContext, services) =>
                {
                    services.AddHostedService<ChaoXingListenerService>();
                });
    }

    public class ChaoXingListenerService : BackgroundService
    {
        private readonly ILogger<ChaoXingListenerService> _logger;
        private HttpListener _listener;
        private readonly string _url = "http://127.0.0.1:56123/";
        private string _dataPath;
        private bool _isLaunchingApp = false; // 标记是否正在尝试启动应用
        private readonly int _ipcPort = 56124; // IPC通信端口
        private readonly string _pipeName = "VICChaoXingPipe"; // 命名管道名称
        private string _secretKey = "p9a_g66dpx9KwB9QH25X"; // 学校管理平台分配的秘钥，请替换为实际的密钥

        // 进程跟踪相关（简化版本，主要用于统计和日志）
        private readonly List<int> _launchedProcessIds = new List<int>(); // 跟踪启动的进程ID
        private readonly object _processLock = new object(); // 进程列表锁
        private Timer _processCleanupTimer; // 定期清理计时器
        private string _vicLauncherPath = null; // VIC_Launcher.exe的路径缓存

        public ChaoXingListenerService(ILogger<ChaoXingListenerService> logger)
        {
            _logger = logger;
            // 初始化公共数据目录
            _dataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.CommonApplicationData), "VICUnity");

            // 初始化进程清理计时器，每30秒清理一次已退出的进程引用
            _processCleanupTimer = new Timer(CleanupExitedProcesses, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("ChaoXing回调监听器启动中...");
            InitializeDataPath();
            // 移除了SetupSslCertificate()调用
            _listener = new HttpListener();
            _listener.Prefixes.Add(_url);
            try
            {
                _listener.Start();
                _logger.LogInformation($"监听器已启动，正在监听:{_url}");
                // 循环等待并处理请求
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        var contextTask = _listener.GetContextAsync();
                        var completedTask = await Task.WhenAny(contextTask, Task.Delay(Timeout.Infinite, stoppingToken));
                        if (stoppingToken.IsCancellationRequested)
                        {
                            break;
                        }
                        if (completedTask == contextTask)
                        {
                            HttpListenerContext context = await contextTask;
                            await ProcessRequestAsync(context, stoppingToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常取消，不再处理
                        break;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "处理请求时出错");
                    }
                }
            }
            catch (HttpListenerException ex)
            {
                _logger.LogError(ex, "启动监听器失败");
                _logger.LogWarning("可能需要以管理员权限运行或配置URL保留。请尝试以管理员身份运行以下命令:");
                _logger.LogWarning("netsh http add urlacl url=http://127.0.0.1:56123/user=Everyone");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "发生异常");
            }
            finally
            {
                StopListener();
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("服务正在停止...");

            // 停止HTTP监听器
            StopListener();

            // 停止进程清理计时器
            _processCleanupTimer?.Dispose();

            // 清理启动的进程
            await CleanupAllLaunchedProcesses();

            // 清理可能残留的VIC相关进程
            await CleanupVICRelatedProcesses();

            // 清理可能残留的锁文件
            CleanupLockFiles();

            await base.StopAsync(cancellationToken);
        }

        private void StopListener()
        {
            if (_listener != null && _listener.IsListening)
            {
                _listener.Close();
                _logger.LogInformation("监听器已关闭");
            }
        }



        /// <summary>
        /// 验证handshake参数
        /// 验证公式: md5(secret_key+fid+resourceId+userId+confSubjectId+date('yyyyMMdd')+classId)
        /// </summary>
        /// <param name="handshake">接收到的handshake值</param>
        /// <param name="fid">管理平台单位ID</param>
        /// <param name="resourceId">管理平台添加的资源ID</param>
        /// <param name="userId">管理平台用户ID</param>
        /// <param name="confSubjectId">管理平台课程ID</param>
        /// <param name="classId">管理平台班级ID</param>
        /// <returns>验证是否通过</returns>
        private bool ValidateHandshake(string handshake, string fid, string resourceId, string userId, string confSubjectId, string classId)
        {
            try
            {
                // 获取当前日期，格式为yyyyMMdd
                string currentDate = DateTime.Now.ToString("yyyyMMdd");

                // 构建验证字符串: secret_key+fid+resourceId+userId+confSubjectId+date('yyyyMMdd')+classId
                string verifyString = $"{_secretKey}{fid}{resourceId}{userId}{confSubjectId}{currentDate}{classId}";

                // 计算MD5哈希
                string calculatedHash = CalculateMD5Hash(verifyString);

                _logger.LogDebug($"handshake验证 - 原始字符串: {verifyString}");
                _logger.LogDebug($"handshake验证 - 计算哈希: {calculatedHash}");
                _logger.LogDebug($"handshake验证 - 接收哈希: {handshake}");

                // 比较哈希值（不区分大小写）
                bool isValid = string.Equals(calculatedHash, handshake, StringComparison.OrdinalIgnoreCase);

                if (!isValid)
                {
                    // 尝试前一天的日期（考虑跨日期的情况）
                    string previousDate = DateTime.Now.AddDays(-1).ToString("yyyyMMdd");
                    string verifyStringPrevious = $"{_secretKey}{fid}{resourceId}{userId}{confSubjectId}{previousDate}{classId}";
                    string calculatedHashPrevious = CalculateMD5Hash(verifyStringPrevious);

                    _logger.LogDebug($"handshake验证(前一天) - 原始字符串: {verifyStringPrevious}");
                    _logger.LogDebug($"handshake验证(前一天) - 计算哈希: {calculatedHashPrevious}");

                    isValid = string.Equals(calculatedHashPrevious, handshake, StringComparison.OrdinalIgnoreCase);
                }

                return isValid;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "handshake验证过程中发生错误");
                return false;
            }
        }

        /// <summary>
        /// 计算字符串的MD5哈希值
        /// </summary>
        /// <param name="input">输入字符串</param>
        /// <returns>MD5哈希值（小写）</returns>
        private string CalculateMD5Hash(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] inputBytes = Encoding.UTF8.GetBytes(input);
                byte[] hashBytes = md5.ComputeHash(inputBytes);

                // 转换为十六进制字符串（小写）
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < hashBytes.Length; i++)
                {
                    sb.Append(hashBytes[i].ToString("x2"));
                }
                return sb.ToString();
            }
        }

        /// <summary>
        /// 初始化数据目录，若失败则使用临时目录
        /// </summary>
        private void InitializeDataPath()
        {
            _logger.LogInformation($"使用数据路径:{_dataPath}");
            try
            {
                Directory.CreateDirectory(_dataPath);
                _logger.LogInformation($"成功创建目录:{_dataPath}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"创建目录失败:{_dataPath}");
                _dataPath = Path.Combine(Path.GetTempPath(), "VICUnity");
                _logger.LogInformation($"尝试使用备用路径:{_dataPath}");
                Directory.CreateDirectory(_dataPath);
            }
        }

        /// <summary>
        /// 处理HTTP请求，根据不同的请求路径进行处理
        /// </summary>
        private async Task ProcessRequestAsync(HttpListenerContext context, CancellationToken stoppingToken)
        {
            HttpListenerRequest request = context.Request;
            HttpListenerResponse response = context.Response;
            _logger.LogInformation($"收到请求:{request.Url.PathAndQuery}");
            _logger.LogInformation($"请求路径: {request.Url.AbsolutePath}");
            _logger.LogInformation($"路径匹配检查 - StartsWith('/login/ChaoXing2'): {request.Url.AbsolutePath.StartsWith("/login/ChaoXing2")}");
            _logger.LogInformation($"路径匹配检查 - StartsWith('/login/oauth2'): {request.Url.AbsolutePath.StartsWith("/login/oauth2")}");
            try
            {
                // 检查是否为根路径请求，返回HTML界面
                if (request.Url.AbsolutePath == "/" || request.Url.AbsolutePath == "/index.html")
                {
                    string htmlResponse = GetHtmlInterface();
                    byte[] buffer = Encoding.UTF8.GetBytes(htmlResponse);
                    response.ContentLength64 = buffer.Length;
                    response.ContentType = "text/html;charset=utf-8";
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length, stoppingToken);
                }
                // 检查是否为登录ChaoXing2请求
                else if (request.Url.AbsolutePath.StartsWith("/login/ChaoXing2") || request.Url.AbsolutePath.StartsWith("/login/oauth2"))
                {
                    // 解析查询参数
                    NameValueCollection queryParams = HttpUtility.ParseQueryString(request.Url.Query);

                    // 提取所需参数
                    string fid = queryParams["fid"] ?? "";
                    string confSubjectId = queryParams["confSubjectId"] ?? "";
                    string classId = queryParams["classId"] ?? "";
                    string userId = queryParams["userId"] ?? "";
                    string usertype = queryParams["usertype"] ?? "";
                    string resourceId = queryParams["resourceId"] ?? "";
                    string handshake = queryParams["handshake"] ?? "";
                    string mode = queryParams["mode"] ?? "";

                    _logger.LogInformation($"收到ChaoXing2登录请求 - userId:{userId}, resourceId:{resourceId}, confSubjectId:{confSubjectId}");

                    // 验证handshake参数
                    if (!string.IsNullOrEmpty(handshake) && !string.IsNullOrEmpty(_secretKey))
                    {
                        bool isValidHandshake = ValidateHandshake(handshake, fid, resourceId, userId, confSubjectId, classId);
                        if (!isValidHandshake)
                        {
                            _logger.LogWarning($"handshake验证失败 - userId:{userId}, handshake:{handshake}");
                            string errorResponse = GetErrorJson("fail", "handshake验证失败，请检查参数是否正确");
                            byte[] errorBuffer = Encoding.UTF8.GetBytes(errorResponse);
                            response.ContentLength64 = errorBuffer.Length;
                            response.ContentType = "application/json;charset=utf-8";
                            await response.OutputStream.WriteAsync(errorBuffer, 0, errorBuffer.Length, stoppingToken);
                            return;
                        }
                        _logger.LogInformation("handshake验证通过");
                    }
                    else if (!string.IsNullOrEmpty(handshake))
                    {
                        _logger.LogWarning("收到handshake参数但secret_key未配置，跳过验证");
                    }

                    // 创建授权数据对象
                    var authData = new
                    {
                        fid = fid,
                        confSubjectId = confSubjectId,
                        classId = classId,
                        userId = userId,
                        usertype = usertype,
                        resourceId = resourceId,
                        handshake = handshake,
                        mode = mode,
                        timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                        source = "ChaoXing2_login"
                    };

                    // 尝试向正在运行的程序传输授权数据
                    bool sentToRunningApp = await TryTransmitToRunningApplication(authData);

                    // 构建自定义协议URL
                    var urlParams = new List<string>();
                    if (!string.IsNullOrEmpty(fid)) urlParams.Add($"fid={Uri.EscapeDataString(fid)}");
                    if (!string.IsNullOrEmpty(confSubjectId)) urlParams.Add($"confSubjectId={Uri.EscapeDataString(confSubjectId)}");
                    if (!string.IsNullOrEmpty(classId)) urlParams.Add($"classId={Uri.EscapeDataString(classId)}");
                    if (!string.IsNullOrEmpty(userId)) urlParams.Add($"userId={Uri.EscapeDataString(userId)}");
                    if (!string.IsNullOrEmpty(usertype)) urlParams.Add($"usertype={Uri.EscapeDataString(usertype)}");
                    if (!string.IsNullOrEmpty(resourceId)) urlParams.Add($"resourceId={Uri.EscapeDataString(resourceId)}");
                    if (!string.IsNullOrEmpty(handshake)) urlParams.Add($"handshake={Uri.EscapeDataString(handshake)}");
                    if (!string.IsNullOrEmpty(mode)) urlParams.Add($"mode={Uri.EscapeDataString(mode)}");
                    urlParams.Add($"timestamp={Uri.EscapeDataString(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))}");

                    string customProtocolUrl = $"vic://VIC_Launcher?{string.Join("&", urlParams)}";

                    // 【修改】不再由监听服务主动启动应用，避免双重启动
                    // 只有在成功传输到运行中的应用时才记录，否则让网页端负责启动
                    if (sentToRunningApp)
                    {
                        _logger.LogInformation("数据已传输到运行中的应用，无需启动新实例");
                    }
                    else
                    {
                        _logger.LogInformation("未找到运行中的应用，将由网页端通过协议启动");
                    }

                    // 返回HTML格式的成功响应（用户友好界面）
                    // sentToRunningApp参数现在表示是否需要网页端启动应用
                    string htmlSuccessResponse = GetSuccessHtml("登录成功", customProtocolUrl, sentToRunningApp);
                    byte[] buffer = Encoding.UTF8.GetBytes(htmlSuccessResponse);
                    response.ContentLength64 = buffer.Length;
                    response.ContentType = "text/html;charset=utf-8";
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length, stoppingToken);
                }
                else
                {
                    // 返回HTML格式的错误响应
                    string htmlErrorResponse = GetErrorHtml($"不支持的请求路径: {request.Url.AbsolutePath}");
                    byte[] buffer = Encoding.UTF8.GetBytes(htmlErrorResponse);
                    response.ContentLength64 = buffer.Length;
                    response.ContentType = "text/html;charset=utf-8";
                    await response.OutputStream.WriteAsync(buffer, 0, buffer.Length, stoppingToken);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "处理请求时出错");
                // 返回HTML格式的错误响应
                string htmlErrorResponse = GetErrorHtml($"处理请求时出错: {ex.Message}");
                byte[] buffer = Encoding.UTF8.GetBytes(htmlErrorResponse);
                response.ContentLength64 = buffer.Length;
                response.ContentType = "text/html;charset=utf-8";
                await response.OutputStream.WriteAsync(buffer, 0, buffer.Length, stoppingToken);
            }
            finally
            {
                response.Close();
            }
        }

        /// <summary>
        /// 尝试向正在运行的应用程序传输授权数据
        /// 使用多种IPC方式：命名管道、TCP Socket、文件共享
        /// </summary>
        private async Task<bool> TryTransmitToRunningApplication(object authData)
        {
            string jsonData = JsonSerializer.Serialize(authData);

            // 方式1: 尝试通过命名管道传输
            if (await TryTransmitViaNamedPipe(jsonData))
            {
                _logger.LogInformation("成功通过命名管道传输授权数据");
                return true;
            }

            // 方式2: 尝试通过TCP Socket传输
            if (await TryTransmitViaTcpSocket(jsonData))
            {
                _logger.LogInformation("成功通过TCP Socket传输授权数据");
                return true;
            }

            // 方式3: 尝试通过文件共享传输
            if (await TryTransmitViaFileShare(jsonData))
            {
                _logger.LogInformation("成功通过文件共享传输授权数据");
                return true;
            }

            _logger.LogWarning("所有IPC方式都失败，未找到正在运行的应用程序");
            return false;
        }

        /// <summary>
        /// 通过命名管道传输数据
        /// </summary>
        private async Task<bool> TryTransmitViaNamedPipe(string data)
        {
            try
            {
                using (var pipeClient = new NamedPipeClientStream(".", _pipeName, PipeDirection.Out))
                {
                    // 尝试连接，超时时间1秒
                    await pipeClient.ConnectAsync(1000);

                    byte[] buffer = Encoding.UTF8.GetBytes(data);
                    await pipeClient.WriteAsync(buffer, 0, buffer.Length);
                    await pipeClient.FlushAsync();

                    _logger.LogInformation($"通过命名管道 {_pipeName} 发送数据成功");
                    return true;
                }
            }
            catch (TimeoutException)
            {
                _logger.LogDebug("命名管道连接超时，可能没有应用程序在监听");
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "命名管道传输失败");
            }
            return false;
        }

        /// <summary>
        /// 通过TCP Socket传输数据
        /// </summary>
        private async Task<bool> TryTransmitViaTcpSocket(string data)
        {
            try
            {
                using (var tcpClient = new TcpClient())
                {
                    // 尝试连接到本地IPC端口，超时时间1秒
                    var connectTask = tcpClient.ConnectAsync(IPAddress.Loopback, _ipcPort);
                    if (await Task.WhenAny(connectTask, Task.Delay(1000)) == connectTask)
                    {
                        using (var stream = tcpClient.GetStream())
                        {
                            byte[] buffer = Encoding.UTF8.GetBytes(data);
                            await stream.WriteAsync(buffer, 0, buffer.Length);
                            await stream.FlushAsync();

                            _logger.LogInformation($"通过TCP Socket端口 {_ipcPort} 发送数据成功");
                            return true;
                        }
                    }
                    else
                    {
                        _logger.LogDebug("TCP Socket连接超时");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "TCP Socket传输失败");
            }
            return false;
        }

        /// <summary>
        /// 通过文件共享传输数据
        /// 注意：不使用文件监控，避免二次登录时读取到上一次的授权码
        /// </summary>
        private async Task<bool> TryTransmitViaFileShare(string data)
        {
            try
            {
                string ipcFilePath = Path.Combine(_dataPath, "ChaoXing_callback.json");
                string lockFilePath = Path.Combine(_dataPath, "ChaoXing_callback.lock");

                // 检查是否有应用程序在监听文件变化（通过检查锁文件的存在）
                if (File.Exists(lockFilePath))
                {
                    // 为避免二次登录读取到旧数据，先删除旧的IPC文件
                    if (File.Exists(ipcFilePath))
                    {
                        File.Delete(ipcFilePath);
                    }

                    // 写入新的授权数据到IPC文件
                    await File.WriteAllTextAsync(ipcFilePath, data);

                    // 等待一小段时间，看文件是否被处理（被删除或修改）
                    await Task.Delay(500);

                    if (!File.Exists(ipcFilePath) || File.GetLastWriteTime(ipcFilePath) != File.GetCreationTime(ipcFilePath))
                    {
                        _logger.LogInformation("通过文件共享发送数据成功");
                        return true;
                    }
                }

                _logger.LogDebug("没有检测到应用程序在监听文件共享");
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "文件共享传输失败");
            }
            return false;
        }

        /// <summary>
        /// 查找VIC_Launcher.exe的安装路径
        /// </summary>
        /// <returns>VIC_Launcher.exe的完整路径，如果未找到则返回null</returns>
        private string FindVICLauncherPath()
        {
            if (!string.IsNullOrEmpty(_vicLauncherPath) && File.Exists(_vicLauncherPath))
            {
                return _vicLauncherPath;
            }

            try
            {
                // 方法1: 从vic协议注册表中获取路径
                using (var key = Registry.ClassesRoot.OpenSubKey(@"vic\shell\open\command"))
                {
                    if (key != null)
                    {
                        string command = key.GetValue("")?.ToString();
                        if (!string.IsNullOrEmpty(command))
                        {
                            // 解析命令行，提取exe路径
                            // 格式通常是: "C:\Program Files\应用名\VIC_Launcher.exe" "%1"
                            var match = System.Text.RegularExpressions.Regex.Match(command, @"""([^""]+\.exe)""");
                            if (match.Success)
                            {
                                string exePath = match.Groups[1].Value;
                                if (File.Exists(exePath))
                                {
                                    _vicLauncherPath = exePath;
                                    _logger.LogInformation($"从注册表找到VIC_Launcher路径: {exePath}");
                                    return exePath;
                                }
                            }
                        }
                    }
                }

                // 方法2: 从卸载信息中查找
                string[] uninstallKeys = {
                    @"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall",
                    @"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"
                };

                foreach (string uninstallKey in uninstallKeys)
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(uninstallKey))
                    {
                        if (key != null)
                        {
                            foreach (string subKeyName in key.GetSubKeyNames())
                            {
                                using (var subKey = key.OpenSubKey(subKeyName))
                                {
                                    if (subKey != null)
                                    {
                                        string displayName = subKey.GetValue("DisplayName")?.ToString();
                                        if (!string.IsNullOrEmpty(displayName) &&
                                            (displayName.Contains("虚拟仿真课程资源平台") ||
                                             displayName.Contains("VIC") ||
                                             displayName.Contains("Launcher")))
                                        {
                                            string installLocation = subKey.GetValue("InstallLocation")?.ToString();
                                            if (!string.IsNullOrEmpty(installLocation))
                                            {
                                                string exePath = Path.Combine(installLocation, "VIC_Launcher.exe");
                                                if (File.Exists(exePath))
                                                {
                                                    _vicLauncherPath = exePath;
                                                    _logger.LogInformation($"从卸载信息找到VIC_Launcher路径: {exePath}");
                                                    return exePath;
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 方法3: 在常见安装目录中搜索
                string[] commonPaths = {
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles), "虚拟仿真课程资源平台", "VIC_Launcher.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86), "虚拟仿真课程资源平台", "VIC_Launcher.exe"),
                    Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "虚拟仿真课程资源平台", "VIC_Launcher.exe")
                };

                foreach (string path in commonPaths)
                {
                    if (File.Exists(path))
                    {
                        _vicLauncherPath = path;
                        _logger.LogInformation($"在常见路径找到VIC_Launcher: {path}");
                        return path;
                    }
                }

                _logger.LogWarning("未能找到VIC_Launcher.exe的安装路径");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "查找VIC_Launcher路径时出错");
                return null;
            }
        }

        private void LaunchApplication(string customProtocolUrl)
        {
            lock (this) // 确保线程安全
            {
                if (!_isLaunchingApp)
                {
                    _isLaunchingApp = true;
                    try
                    {
                        _logger.LogInformation($"启动应用:{customProtocolUrl}");
                        Process launchedProcess = null;
                        bool launchSuccess = false;

                        // 解析协议URL中的参数
                        string protocolArgs = ExtractProtocolArguments(customProtocolUrl);

                        // 方式1: 优先尝试直接启动VIC_Launcher.exe
                        string vicLauncherPath = FindVICLauncherPath();
                        if (!string.IsNullOrEmpty(vicLauncherPath))
                        {
                            try
                            {
                                var processInfo = new ProcessStartInfo
                                {
                                    FileName = vicLauncherPath,
                                    Arguments = protocolArgs, // 直接传递解析后的参数
                                    UseShellExecute = false, // 直接启动，不通过Shell
                                    CreateNoWindow = false,
                                    WindowStyle = ProcessWindowStyle.Normal,
                                    WorkingDirectory = Path.GetDirectoryName(vicLauncherPath)
                                };

                                launchedProcess = Process.Start(processInfo);
                                if (launchedProcess != null)
                                {
                                    RecordLaunchedProcess(launchedProcess.Id);
                                    _logger.LogInformation($"直接启动VIC_Launcher成功，进程ID: {launchedProcess.Id}");
                                    launchSuccess = true;

                                    // 立即释放Process对象，让应用独立运行
                                    launchedProcess.Dispose();
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "直接启动VIC_Launcher失败，尝试协议启动");
                            }
                        }

                        // 方式2: 如果直接启动失败，回退到协议启动
                        if (!launchSuccess)
                        {
                            try
                            {
                                var processInfo = new ProcessStartInfo
                                {
                                    FileName = customProtocolUrl,
                                    UseShellExecute = true,
                                    CreateNoWindow = false,
                                    WindowStyle = ProcessWindowStyle.Normal,
                                    ErrorDialog = false,
                                    LoadUserProfile = false
                                };

                                launchedProcess = Process.Start(processInfo);
                                if (launchedProcess != null)
                                {
                                    // 对于协议启动，我们可能无法直接获取VIC_Launcher的进程ID
                                    // 但我们记录启动的进程ID用于日志
                                    RecordLaunchedProcess(launchedProcess.Id);
                                    _logger.LogInformation($"协议启动成功，启动进程ID: {launchedProcess.Id}");
                                    launchSuccess = true;

                                    // 立即释放Process对象
                                    launchedProcess.Dispose();
                                }
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "协议启动也失败");
                            }
                        }

                        if (!launchSuccess)
                        {
                            _logger.LogError("所有启动方式都失败，无法启动VIC_Launcher");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "启动应用时出错");
                    }
                    finally
                    {
                        _isLaunchingApp = false;
                    }
                }
                else
                {
                    _logger.LogWarning("应用已经在尝试启动，忽略新的启动请求");
                }
            }
        }

        /// <summary>
        /// 从协议URL中提取参数
        /// </summary>
        /// <param name="protocolUrl">协议URL，如: vic://VIC_Launcher?param1=value1&param2=value2</param>
        /// <returns>提取的参数字符串</returns>
        private string ExtractProtocolArguments(string protocolUrl)
        {
            try
            {
                if (string.IsNullOrEmpty(protocolUrl) || !protocolUrl.StartsWith("vic://"))
                {
                    return "";
                }

                var uri = new Uri(protocolUrl);
                string query = uri.Query;

                if (!string.IsNullOrEmpty(query) && query.StartsWith("?"))
                {
                    return query.Substring(1); // 移除开头的?
                }

                return "";
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"解析协议URL参数时出错: {protocolUrl}");
                return "";
            }
        }

        /// <summary>
        /// 记录启动的进程ID（用于统计和日志）
        /// </summary>
        /// <param name="processId">进程ID</param>
        private void RecordLaunchedProcess(int processId)
        {
            lock (_processLock)
            {
                _launchedProcessIds.Add(processId);
                _logger.LogInformation($"已记录启动的进程ID: {processId}，总启动次数: {_launchedProcessIds.Count}");
            }
        }

        /// <summary>
        /// 定期清理已退出的进程ID记录
        /// </summary>
        /// <param name="state">计时器状态</param>
        private void CleanupExitedProcesses(object state)
        {
            lock (_processLock)
            {
                var activeProcessIds = new List<int>();

                foreach (var processId in _launchedProcessIds)
                {
                    try
                    {
                        var process = Process.GetProcessById(processId);
                        if (!process.HasExited)
                        {
                            activeProcessIds.Add(processId);
                        }
                        process.Dispose();
                    }
                    catch
                    {
                        // 进程已经不存在，不需要保留ID
                    }
                }

                int removedCount = _launchedProcessIds.Count - activeProcessIds.Count;
                _launchedProcessIds.Clear();
                _launchedProcessIds.AddRange(activeProcessIds);

                if (removedCount > 0)
                {
                    _logger.LogInformation($"已清理 {removedCount} 个已退出进程的记录，当前活跃进程数: {activeProcessIds.Count}");
                }
            }
        }

        /// <summary>
        /// 清理所有启动的进程（简化版本）
        /// </summary>
        private async Task CleanupAllLaunchedProcesses()
        {
            List<int> processIdsToCleanup;

            lock (_processLock)
            {
                processIdsToCleanup = new List<int>(_launchedProcessIds);
                _launchedProcessIds.Clear();
            }

            if (processIdsToCleanup.Count == 0)
            {
                _logger.LogInformation("没有记录的启动进程需要清理");
                return;
            }

            _logger.LogInformation($"开始检查 {processIdsToCleanup.Count} 个记录的启动进程");

            int cleanedCount = 0;
            foreach (var processId in processIdsToCleanup)
            {
                try
                {
                    var process = Process.GetProcessById(processId);
                    if (!process.HasExited)
                    {
                        _logger.LogInformation($"正在终止记录的进程 {processId} ({process.ProcessName})");

                        // 首先尝试优雅关闭
                        process.CloseMainWindow();

                        // 等待3秒让进程优雅退出
                        if (!process.WaitForExit(3000))
                        {
                            // 如果进程没有在3秒内退出，强制终止
                            _logger.LogWarning($"进程 {processId} 未能优雅退出，强制终止");
                            process.Kill();
                            process.WaitForExit(2000);
                        }

                        _logger.LogInformation($"进程 {processId} 已成功终止");
                        cleanedCount++;
                    }
                    process.Dispose();
                }
                catch (ArgumentException)
                {
                    // 进程已经不存在
                    _logger.LogDebug($"进程 {processId} 已经不存在");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"清理进程 {processId} 时出错");
                }
            }

            _logger.LogInformation($"记录的进程清理完成，实际清理了 {cleanedCount} 个进程");
        }

        /// <summary>
        /// 清理可能残留的锁文件
        /// </summary>
        private void CleanupLockFiles()
        {
            try
            {
                string lockFilePath = Path.Combine(_dataPath, "ChaoXing_callback.lock");
                if (File.Exists(lockFilePath))
                {
                    File.Delete(lockFilePath);
                    _logger.LogInformation("已清理残留的锁文件");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清理锁文件时出错");
            }
        }

        /// <summary>
        /// 查找并清理可能残留的VIC相关进程
        /// </summary>
        private async Task CleanupVICRelatedProcesses()
        {
            try
            {
                _logger.LogInformation("开始检查VIC_Launcher相关的残留进程...");

                var processesToKill = new List<Process>();
                var allProcesses = Process.GetProcesses();

                foreach (var process in allProcesses)
                {
                    try
                    {
                        string processName = process.ProcessName.ToLower();

                        // 精确匹配VIC_Launcher进程
                        if (processName == "vic_launcher" ||
                            processName.Contains("vic_launcher") ||
                            (processName.Contains("vic") && processName.Contains("launcher")))
                        {
                            processesToKill.Add(process);
                            _logger.LogInformation($"发现VIC_Launcher残留进程: {process.ProcessName} (PID: {process.Id})");
                        }
                        // 检查可能与VIC协议相关的rundll32进程
                        else if (processName == "rundll32" && IsVICRelatedRundll32(process))
                        {
                            processesToKill.Add(process);
                            _logger.LogInformation($"发现VIC协议相关的rundll32进程: (PID: {process.Id})");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 某些系统进程可能无法访问，忽略这些错误
                        _logger.LogDebug(ex, $"检查进程时出错，忽略");
                    }
                }

                // 清理发现的进程
                foreach (var process in processesToKill)
                {
                    try
                    {
                        if (!process.HasExited)
                        {
                            _logger.LogInformation($"正在清理残留进程: {process.ProcessName} (PID: {process.Id})");

                            // 对于VIC_Launcher，尝试优雅关闭
                            if (process.ProcessName.ToLower().Contains("vic_launcher"))
                            {
                                process.CloseMainWindow();
                                if (!process.WaitForExit(5000)) // 给VIC_Launcher更多时间优雅退出
                                {
                                    _logger.LogWarning($"VIC_Launcher进程 {process.Id} 未能优雅退出，强制终止");
                                    process.Kill();
                                    process.WaitForExit(2000);
                                }
                            }
                            else
                            {
                                // 对于其他进程（如rundll32），直接终止
                                process.Kill();
                                process.WaitForExit(2000);
                            }

                            _logger.LogInformation($"已清理残留进程: {process.ProcessName} (PID: {process.Id})");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, $"清理进程 {process.ProcessName} (PID: {process.Id}) 时出错");
                    }
                    finally
                    {
                        try
                        {
                            process.Dispose();
                        }
                        catch { }
                    }
                }

                // 清理allProcesses数组
                foreach (var process in allProcesses)
                {
                    try
                    {
                        process.Dispose();
                    }
                    catch { }
                }

                if (processesToKill.Count > 0)
                {
                    _logger.LogInformation($"VIC_Launcher相关进程清理完成，共清理 {processesToKill.Count} 个进程");
                }
                else
                {
                    _logger.LogInformation("未发现VIC_Launcher相关的残留进程");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "清理VIC_Launcher相关进程时出错");
            }
        }

        /// <summary>
        /// 检查rundll32进程是否与VIC相关
        /// </summary>
        private bool IsVICRelatedRundll32(Process process)
        {
            try
            {
                // 检查命令行参数是否包含vic协议
                string commandLine = GetProcessCommandLine(process);
                return commandLine != null && commandLine.ToLower().Contains("vic://");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 检查进程是否确实与VIC相关
        /// </summary>
        private bool IsVICRelatedProcess(Process process)
        {
            try
            {
                // 检查进程的可执行文件路径或命令行
                string processName = process.ProcessName.ToLower();

                // 检查是否是VIC_Launcher或相关进程
                if (processName.Contains("vic_launcher") ||
                    processName.Contains("vicunity") ||
                    processName.StartsWith("vic"))
                {
                    return true;
                }

                // 检查命令行参数
                string commandLine = GetProcessCommandLine(process);
                if (commandLine != null)
                {
                    string cmdLower = commandLine.ToLower();
                    if (cmdLower.Contains("vic://") ||
                        cmdLower.Contains("vic_launcher") ||
                        cmdLower.Contains("vicunity"))
                    {
                        return true;
                    }
                }

                return false;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取进程的命令行参数（简化版本）
        /// </summary>
        private string GetProcessCommandLine(Process process)
        {
            try
            {
                // 这是一个简化的实现，在实际环境中可能需要使用WMI或其他方法
                // 这里只是尝试获取基本信息
                return process.StartInfo.Arguments;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 生成主HTML界面
        /// </summary>
        /// <returns>HTML格式的主界面</returns>
        private string GetHtmlInterface()
        {
            return $@"<!DOCTYPE html>
<html lang=""zh-CN"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>超星ChaoXing回调监听器</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }}
        .logo {{
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }}
        h1 {{
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }}
        .subtitle {{
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }}
        .status {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }}
        .status-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }}
        .status-label {{
            font-weight: bold;
            color: #333;
        }}
        .status-value {{
            color: #28a745;
            font-weight: 500;
        }}
        .info-box {{
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            text-align: left;
        }}
        .info-title {{
            color: #1976d2;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .info-text {{
            color: #333;
            line-height: 1.6;
        }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 14px;
        }}
        .pulse {{
            animation: pulse 2s infinite;
        }}
        @keyframes pulse {{
            0% {{ transform: scale(1); }}
            50% {{ transform: scale(1.05); }}
            100% {{ transform: scale(1); }}
        }}
    </style>
    <script>
        // 5秒后自动关闭页面
        setTimeout(function() {{
            window.close();
        }}, 5000);
    </script>
</head>
<body>
    <div class=""container"">
        <div class=""logo pulse"">超</div>
        <h1>超星ChaoXing回调监听器</h1>
        <p class=""subtitle"">ChaoXing ChaoXing Callback Listener</p>
        
        <div class=""status"">
            <div class=""status-item"">
                <span class=""status-label"">服务状态</span>
                <span class=""status-value"">🟢 运行中</span>
            </div>
            <div class=""status-item"">
                <span class=""status-label"">监听地址</span>
                <span class=""status-value"">{_url}</span>
            </div>
            <div class=""status-item"">
                <span class=""status-label"">启动时间</span>
                <span class=""status-value"">{DateTime.Now:yyyy-MM-dd HH:mm:ss}</span>
            </div>
        </div>
        
        <div class=""info-box"">
            <div class=""info-title"">📋 使用说明</div>
            <div class=""info-text"">
                • 此服务用于接收超星学习通的ChaoXing回调请求<br>
                • 支持自动启动VIC Unity应用程序<br>
                • 支持多种IPC通信方式（命名管道、TCP Socket、文件共享）<br>
                • ChaoXing2登录地址：<code>{_url}login/ChaoXing2</code>
            </div>
        </div>
        
        <div class=""footer"">
            <p>VIC Unity ChaoXing Callback Listener v1.0</p>
            <p>© 2024 VIC Unity Project</p>
        </div>
    </div>
</body>
</html>";
        }

        /// <summary>
        /// 生成成功的HTML响应
        /// </summary>
        /// <param name="message">成功消息</param>
        /// <param name="customProtocolUrl">自定义协议URL</param>
        /// <param name="sentToRunningApp">是否已发送到运行中的应用</param>
        /// <returns>HTML格式的成功响应</returns>
        private string GetSuccessHtml(string message, string customProtocolUrl, bool sentToRunningApp = false)
        {
            // 确保变量值被正确替换
            string appStatus = sentToRunningApp ? "数据已发送到运行中的应用" : "准备启动应用";
            string statusIcon = sentToRunningApp ? "🔄" : "🚀";
            string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // 根据是否已发送到运行中的应用来决定是否自动启动
            bool shouldAutoLaunch = !sentToRunningApp;

            return $@"<!DOCTYPE html>
<html lang=""zh-CN"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>登录成功 - 超星ChaoXing回调</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }}
        .success-icon {{
            width: 80px;
            height: 80px;
            background: #4CAF50;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            animation: bounce 1s ease-in-out;
        }}
        h1 {{
            color: #4CAF50;
            margin-bottom: 10px;
            font-size: 28px;
        }}
        .subtitle {{
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }}
        .info-box {{
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }}
        .info-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }}
        .info-label {{
            font-weight: bold;
            color: #333;
        }}
        .info-value {{
            color: #4CAF50;
            font-weight: 500;
        }}
        .protocol-url {{
            background: #e8f5e8;
            border: 1px solid #4CAF50;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            word-break: break-all;
            font-family: monospace;
            font-size: 12px;
            color: #2e7d32;
        }}
        .btn {{
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }}
        .btn:hover {{
            background: #45a049;
        }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 14px;
        }}
        @keyframes bounce {{
            0%, 20%, 60%, 100% {{ transform: translateY(0); }}
            40% {{ transform: translateY(-10px); }}
            80% {{ transform: translateY(-5px); }}
        }}
    </style>
    <script>
        function launchApp() {{
            window.location.href = '{customProtocolUrl}';
        }}
        
        function copyUrl() {{
            navigator.clipboard.writeText('{customProtocolUrl}').then(function() {{
                alert('协议URL已复制到剪贴板');
            }});
        }}
        
        // 根据IPC状态决定是否自动启动应用
        " + (shouldAutoLaunch ? "setTimeout(launchApp, 2000);" : "console.log('应用已在运行，无需启动新实例');") + @"

        // 5秒后自动关闭页面
        setTimeout(function() {
            window.close();
        }, 5000);
    </script>
</head>
<body>
    <div class=""container"">
        <div class=""success-icon"">✓</div>
        <h1>{message}</h1>
        <p class=""subtitle"">ChaoXing认证已完成</p>
        
        <div class=""info-box"">
            <div class=""info-item"">
                <span class=""info-label"">处理状态</span>
                <span class=""info-value"">{statusIcon} {appStatus}</span>
            </div>
            <div class=""info-item"">
                <span class=""info-label"">处理时间</span>
                <span class=""info-value"">{currentTime}</span>
            </div>
        </div>
        
        <div class=""protocol-url"">
            <strong>协议URL:</strong><br>
            {customProtocolUrl}
        </div>
        
        " + (shouldAutoLaunch ? @"
        <button class=""btn"" onclick=""launchApp()"">手动启动应用</button>
        <button class=""btn"" onclick=""copyUrl()"">复制URL</button>
        <a href=""/"" class=""btn"" style=""background: #6c757d;"">返回首页</a>

        <div class=""footer"">
            <p>如果应用没有自动启动，请点击""手动启动应用""按钮</p>
        </div>" : @"
        <button class=""btn"" onclick=""copyUrl()"">复制URL</button>
        <a href=""/"" class=""btn"" style=""background: #6c757d;"">返回首页</a>

        <div class=""footer"">
            <p>数据已发送到运行中的应用，无需启动新实例</p>
        </div>") + @"
    </div>
</body>
</html>";
        }

        /// <summary>
        /// 生成错误的HTML响应
        /// </summary>
        /// <param name="message">错误消息</param>
        /// <returns>HTML格式的错误响应</returns>
        private string GetErrorHtml(string message)
        {
            // 确保变量值被正确替换
            string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            return $@"<!DOCTYPE html>
<html lang=""zh-CN"">
<head>
    <meta charset=""UTF-8"">
    <meta name=""viewport"" content=""width=device-width, initial-scale=1.0"">
    <title>错误 - 超星ChaoXing回调</title>
    <style>
        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }}
        .container {{
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
        }}
        .error-icon {{
            width: 80px;
            height: 80px;
            background: #f44336;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 36px;
            animation: shake 0.5s ease-in-out;
        }}
        h1 {{
            color: #f44336;
            margin-bottom: 10px;
            font-size: 28px;
        }}
        .subtitle {{
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }}
        .error-box {{
            background: #ffebee;
            border: 1px solid #f44336;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }}
        .error-title {{
            color: #d32f2f;
            font-weight: bold;
            margin-bottom: 10px;
        }}
        .error-text {{
            color: #333;
            line-height: 1.6;
        }}
        .btn {{
            background: #f44336;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }}
        .btn:hover {{
            background: #d32f2f;
        }}
        .footer {{
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 14px;
        }}
        @keyframes shake {{
            0%, 100% {{ transform: translateX(0); }}
            25% {{ transform: translateX(-5px); }}
            75% {{ transform: translateX(5px); }}
        }}
    </style>
    <script>
        // 5秒后自动关闭页面
        setTimeout(function() {{
            window.close();
        }}, 5000);
    </script>
</head>
<body>
    <div class=""container"">
        <div class=""error-icon"">✗</div>
        <h1>请求失败</h1>
        <p class=""subtitle"">ChaoXing认证过程中发生错误</p>
        
        <div class=""error-box"">
            <div class=""error-title"">❌ 错误详情</div>
            <div class=""error-text"">{message}</div>
        </div>
        
        <div class=""error-box"">
            <div class=""error-title"">💡 解决建议</div>
            <div class=""error-text"">
                • 检查请求URL是否正确<br>
                • 确认所有必需参数都已提供<br>
                • 验证handshake参数是否有效<br>
                • 联系系统管理员获取帮助
            </div>
        </div>
        
        <a href=""/"" class=""btn"">返回首页</a>
        <button class=""btn"" onclick=""history.back()"" style=""background: #6c757d;"">返回上页</button>
        
        <div class=""footer"">
            <p>错误时间: {currentTime}</p>
        </div>
    </div>
</body>
</html>";
        }

        /// <summary>
        /// 生成成功的JSON响应
        /// </summary>
        /// <param name="status">状态：success或fail</param>
        /// <param name="message">返回说明</param>
        /// <param name="customProtocolUrl">自定义协议URL</param>
        /// <param name="sentToRunningApp">是否已发送到运行中的应用</param>
        /// <returns>JSON格式的响应字符串</returns>
        private string GetSuccessJson(string status, string message, string customProtocolUrl, bool sentToRunningApp = false)
        {
            // 确保变量值被正确替换
            string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var responseData = new
            {
                status = status,
                msg = message,
                data = new
                {
                    customProtocolUrl = customProtocolUrl,
                    sentToRunningApp = sentToRunningApp,
                    timestamp = currentTime
                }
            };

            return JsonSerializer.Serialize(responseData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }

        /// <summary>
        /// 生成错误的JSON响应
        /// </summary>
        /// <param name="status">状态：success或fail</param>
        /// <param name="message">错误说明</param>
        /// <returns>JSON格式的响应字符串</returns>
        private string GetErrorJson(string status, string message)
        {
            // 确保变量值被正确替换
            string currentTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            
            var responseData = new
            {
                status = status,
                msg = message,
                timestamp = currentTime
            };

            return JsonSerializer.Serialize(responseData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true,
                Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
            });
        }
    }
}



